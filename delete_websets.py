#!/usr/bin/env python3
import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

API_KEY = os.getenv('EXA_API_KEY')
BASE_URL = 'https://api.exa.ai/websets/v0'

def list_all_websets():
    """List all websets with pagination"""
    headers = {
        'accept': 'application/json',
        'x-api-key': API_KEY
    }
    
    all_websets = []
    cursor = None
    
    while True:
        params = {}
        if cursor:
            params['cursor'] = cursor
            
        response = requests.get(f'{BASE_URL}/websets', headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            websets = data.get('data', [])
            all_websets.extend(websets)
            
            # Check if there's a next cursor for pagination
            pagination = data.get('pagination', {})
            cursor = pagination.get('next_cursor')
            
            if not cursor:
                break
        else:
            print(f"Error listing websets: {response.status_code} - {response.text}")
            return None
    
    return {'data': all_websets}

def delete_webset(webset_id):
    """Delete a specific webset"""
    headers = {
        'accept': 'application/json',
        'x-api-key': API_KEY
    }
    
    response = requests.delete(f'{BASE_URL}/websets/{webset_id}', headers=headers)
    if response.status_code == 200:
        print(f"✅ Successfully deleted webset: {webset_id}")
        return True
    else:
        print(f"❌ Error deleting webset {webset_id}: {response.status_code} - {response.text}")
        return False

def main():
    print("🔍 Listing all websets...")
    websets_data = list_all_websets()
    
    if not websets_data:
        print("No websets found or error occurred.")
        return
    
    websets = websets_data.get('data', [])
    
    if not websets:
        print("No websets found.")
        return
    
    print(f"\n📋 Found {len(websets)} websets:")
    for i, webset in enumerate(websets, 1):
        webset_id = webset.get('id', 'Unknown')
        title = webset.get('title', 'No title')
        status = webset.get('status', 'Unknown status')
        print(f"{i}. ID: {webset_id}")
        print(f"   Title: {title}")
        print(f"   Status: {status}")
        print()
    
    # Proceed with deletion
    print("\n🗑️  Deleting websets...")
    successful_deletions = 0
    
    for webset in websets:
        webset_id = webset.get('id')
        if webset_id:
            if delete_webset(webset_id):
                successful_deletions += 1
    
    print(f"\n✅ Successfully deleted {successful_deletions} out of {len(websets)} websets.")

if __name__ == "__main__":
    main()