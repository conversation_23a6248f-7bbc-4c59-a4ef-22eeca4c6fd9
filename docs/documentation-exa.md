# How Exa Websets Work

Exa Websets is an **asynchronous, event-driven API** for finding, verifying, and processing web data at scale.

## Core Architecture

1. **Webset**: A container that organizes your unique collection of web content
2. **Search Agent**: Searches and crawls the web to find entities matching your criteria
3. **Items**: Structured results with verification status and entity-specific fields
4. **Enrichment Agent**: Enhances items with additional structured data from the web

## Workflow

1. **Create a Webset** with search parameters:
   - Query (e.g., "Top AI research labs focusing on large language models")
   - Count (number of items to find)
   - Entity type (company, person, article, research_paper, or custom)
   - Optional criteria for evaluation

2. **Search Process**:
   - Search agents asynchronously crawl the web
   - Each result is verified against criteria
   - Items include reasoning and references explaining matches
   - Process can take seconds to minutes depending on complexity

3. **Enrichments** (optional):
   - Add enrichments to extract specific data points
   - Example: "LinkedIn profile of VP of Engineering"
   - Formats: text, date, number, options, email, phone

4. **Event-Driven Updates**:
   - Configure webhooks for real-time notifications
   - Events fired when items are found or enrichments complete
   - Process data as it arrives

## Key Features

- **Asynchronous**: Non-blocking API with background processing
- **Structured**: Every item includes structured properties and verification
- **Event-Driven**: Real-time updates via webhooks
- **Scalable**: Handle complex searches across large datasets

## Example Usage

```python
# Create webset with search and enrichments
webset = exa.websets.create(
    params=CreateWebsetParameters(
        search={
            "query": "Top AI research labs focusing on large language models",
            "count": 5
        },
        enrichments=[
            CreateEnrichmentParameters(
                description="LinkedIn profile of VP of Engineering",
                format="text",
            ),
        ],
    )
)

# Wait for completion
webset = exa.websets.wait_until_idle(webset.id)

# Retrieve items
items = exa.websets.items.list(webset_id=webset.id)
```

The API is designed for building custom web data pipelines with precise entity extraction and enrichment capabilities.