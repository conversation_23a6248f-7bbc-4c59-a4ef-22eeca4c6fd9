from exa_py import Exa
from exa_py.websets.types import CreateWebsetParameters, CreateEnrichmentParameters, CreateCriterionParameters
import os
import json

exa = Exa(os.getenv('EXA_API_KEY'))

webset = exa.websets.create(
    params=CreateWebsetParameters(
        search={
            "query": "women/female entrepreneurs in california",
            "criteria": [
                CreateCriterionParameters(description="identifies as a woman or female"),
                CreateCriterionParameters(description="entrepreneur (founder or co-founder of a business)"),
                CreateCriterionParameters(description="based in california")
            ],
            "count": 5
        },
        enrichments=[
            CreateEnrichmentParameters(
                description="Email Address",
                format="text",
            ),
            CreateEnrichmentParameters(
                description="Phone Number",
                format="text",
            ),
        ]
    )
)

# Save response to JSON file
with open('response.json', 'w') as f:
    json.dump(webset.model_dump() if hasattr(webset, 'model_dump') else webset.__dict__, f, indent=2, default=str)