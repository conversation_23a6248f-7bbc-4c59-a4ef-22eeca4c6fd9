# LeadStream MVP

A simple lead generation tool that demonstrates Exa Websets capabilities for finding and enriching professional contacts.

## Quick Start

### Backend
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload
```

### Frontend
```bash
cd frontend
npm install
npm run dev
```

## Features

- Natural language search for professionals (e.g., "CTOs at AI startups in San Francisco")
- Real-time results display
- Email and phone enrichment
- CSV export of results

## API Endpoints

- `POST /search` - Create new search
- `GET /search/{id}/status` - Check search status
- `POST /enrich` - Enrich selected leads
- `GET /results/{id}` - Get search results

## Environment Variables

Create a `.env` file in the backend directory:
```
EXA_API_KEY=your_api_key_here
```