from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import httpx
import os
from dotenv import load_dotenv
import asyncio
from datetime import datetime

load_dotenv()

app = FastAPI(title="LeadStream MVP")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
EXA_API_KEY = os.getenv("EXA_API_KEY")
EXA_BASE_URL = "https://api.exa.ai/websets/v0"

# In-memory storage (no database for MVP)
searches = {}
results = {}

# Pydantic models
class SearchRequest(BaseModel):
    query: str

class SearchResponse(BaseModel):
    search_id: str
    status: str
    message: str

class EnrichRequest(BaseModel):
    item_ids: List[str]
    search_id: str

class SearchStatus(BaseModel):
    search_id: str
    status: str
    progress: Optional[int] = None
    total: Optional[int] = None

# Helper functions
async def create_exa_webset(query: str) -> Dict[str, Any]:
    """Create a webset in Exa for finding people"""
    headers = {
        "x-api-key": EXA_API_KEY,
        "Content-Type": "application/json"
    }
    
    payload = {
        "search": {
            "query": query,
            "count": 50
        }
    }
    
    print(f"Creating webset with query: {query}")
    print(f"Using API key: {EXA_API_KEY[:10]}...")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(
                f"{EXA_BASE_URL}/websets/",
                headers=headers,
                json=payload
            )
            
            print(f"Response status: {response.status_code}")
            print(f"Response text: {response.text}")
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"Exa API error: {response.text}")
            
            return response.json()
            
        except httpx.TimeoutException:
            raise HTTPException(status_code=408, detail="Request to Exa API timed out")
        except Exception as e:
            print(f"Error calling Exa API: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error calling Exa API: {str(e)}")

async def get_webset_status(webset_id: str) -> Dict[str, Any]:
    """Check the status of a webset"""
    headers = {
        "x-api-key": EXA_API_KEY
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{EXA_BASE_URL}/websets/{webset_id}",
            headers=headers
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        
        return response.json()

async def get_webset_items(webset_id: str) -> List[Dict[str, Any]]:
    """Get all items from a completed webset"""
    headers = {
        "x-api-key": EXA_API_KEY
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{EXA_BASE_URL}/websets/{webset_id}/items",
            headers=headers
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        
        return response.json().get("items", [])

async def enrich_items(item_ids: List[str], webset_id: str) -> Dict[str, Any]:
    """Enrich items with email and phone information"""
    headers = {
        "x-api-key": EXA_API_KEY,
        "Content-Type": "application/json"
    }
    
    # Create enrichment for emails
    email_payload = {
        "targetItemIds": item_ids,
        "formats": [
            {
                "type": "email",
                "propertyName": "email"
            },
            {
                "type": "phone",
                "propertyName": "phone"
            }
        ]
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{EXA_BASE_URL}/websets/{webset_id}/enrichments",
            headers=headers,
            json=email_payload
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        
        enrichment_data = response.json()
        enrichment_id = enrichment_data["id"]
        
        # Wait for enrichment to complete (simplified polling)
        max_attempts = 30
        for _ in range(max_attempts):
            enrichment_status = await client.get(
                f"{EXA_BASE_URL}/websets/{webset_id}/enrichments/{enrichment_id}",
                headers=headers
            )
            
            if enrichment_status.status_code == 200:
                status_data = enrichment_status.json()
                if status_data.get("status") == "completed":
                    return status_data
            
            await asyncio.sleep(2)
        
        raise HTTPException(status_code=408, detail="Enrichment timeout")

# API Endpoints
@app.get("/")
async def root():
    return {"message": "LeadStream MVP API", "version": "1.0.0"}

@app.post("/search", response_model=SearchResponse)
async def create_search(request: SearchRequest):
    """Create a new search for professionals"""
    print(f"Received search request: {request.query}")
    
    # Check if API key is configured
    if not EXA_API_KEY or EXA_API_KEY == "your_exa_api_key_here":
        raise HTTPException(
            status_code=400, 
            detail="Exa API key not configured. Please add your API key to backend/.env file"
        )
    
    try:
        # Create webset in Exa
        webset_data = await create_exa_webset(request.query)
        search_id = webset_data["id"]
        
        # Store search info
        searches[search_id] = {
            "query": request.query,
            "status": "processing",
            "created_at": datetime.now().isoformat(),
            "webset_data": webset_data
        }
        
        return SearchResponse(
            search_id=search_id,
            status="processing",
            message=f"Search created successfully. Searching for: {request.query}"
        )
        
    except Exception as e:
        print(f"Error in create_search: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/search/{search_id}/status", response_model=SearchStatus)
async def get_search_status(search_id: str):
    """Check the status of a search"""
    if search_id not in searches:
        raise HTTPException(status_code=404, detail="Search not found")
    
    try:
        # Get current status from Exa
        webset_status = await get_webset_status(search_id)
        
        # Update local status
        searches[search_id]["status"] = webset_status.get("status", "processing")
        
        # Calculate progress
        total_items = webset_status.get("totalItemCount", 0)
        completed_searches = len([s for s in webset_status.get("searches", []) 
                                if s.get("status") == "completed"])
        total_searches = len(webset_status.get("searches", []))
        
        return SearchStatus(
            search_id=search_id,
            status=webset_status.get("status", "processing"),
            progress=completed_searches,
            total=total_searches
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/results/{search_id}")
async def get_results(search_id: str):
    """Get search results"""
    if search_id not in searches:
        raise HTTPException(status_code=404, detail="Search not found")
    
    try:
        # Check if we have cached results
        if search_id in results:
            return {"results": results[search_id], "query": searches[search_id]["query"]}
        
        # Get items from Exa
        items = await get_webset_items(search_id)
        
        # Process and format results
        formatted_results = []
        for item in items:
            result = {
                "id": item.get("id"),
                "name": item.get("name", "Unknown"),
                "position": item.get("position", ""),
                "company": item.get("company", {}).get("name", "") if isinstance(item.get("company"), dict) else "",
                "location": item.get("location", ""),
                "url": item.get("url", ""),
                "pictureUrl": item.get("pictureUrl", ""),
                "email": item.get("email", ""),
                "phone": item.get("phone", "")
            }
            formatted_results.append(result)
        
        # Cache results
        results[search_id] = formatted_results
        
        return {"results": formatted_results, "query": searches[search_id]["query"]}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/enrich")
async def enrich_leads(request: EnrichRequest):
    """Enrich selected leads with contact information"""
    if request.search_id not in searches:
        raise HTTPException(status_code=404, detail="Search not found")
    
    try:
        # Perform enrichment
        enrichment_data = await enrich_items(request.item_ids, request.search_id)
        
        # Update cached results with enriched data
        if request.search_id in results:
            enriched_items = enrichment_data.get("enrichedItems", {})
            
            for result in results[request.search_id]:
                if result["id"] in enriched_items:
                    enriched_data = enriched_items[result["id"]]
                    result["email"] = enriched_data.get("email", result.get("email", ""))
                    result["phone"] = enriched_data.get("phone", result.get("phone", ""))
        
        return {
            "status": "completed",
            "enriched_count": len(enrichment_data.get("enrichedItems", {})),
            "message": "Enrichment completed successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)