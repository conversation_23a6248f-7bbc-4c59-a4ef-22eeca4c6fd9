#!/usr/bin/env python3

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import httpx
import os
from dotenv import load_dotenv
import asyncio
from datetime import datetime

load_dotenv()

app = FastAPI(title="LeadStream API - Real Exa Integration")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
EXA_API_KEY = os.getenv("EXA_API_KEY")
EXA_BASE_URL = "https://api.exa.ai/websets/v0"

# In-memory storage
searches = {}
results = {}

class SearchRequest(BaseModel):
    query: str

class SearchResponse(BaseModel):
    search_id: str
    status: str
    message: str

class EnrichRequest(BaseModel):
    search_id: str
    item_ids: List[str]

# Helper function to create webset
async def create_exa_webset(query: str) -> Dict[str, Any]:
    headers = {
        "x-api-key": EXA_API_KEY,
        "Content-Type": "application/json"
    }
    
    payload = {
        "search": {
            "query": query,
            "count": 3  # Limit to 3 results
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{EXA_BASE_URL}/websets/",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=f"Exa API error: {response.text}")
        
        return response.json()

# Helper to get webset status
async def get_webset_status(webset_id: str) -> Dict[str, Any]:
    headers = {"x-api-key": EXA_API_KEY}
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{EXA_BASE_URL}/websets/{webset_id}",
            headers=headers
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        
        return response.json()

# Helper to get webset items
async def get_webset_items(webset_id: str) -> List[Dict[str, Any]]:
    headers = {"x-api-key": EXA_API_KEY}
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{EXA_BASE_URL}/websets/{webset_id}/items",
            headers=headers
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        
        return response.json().get("data", [])

@app.get("/")
async def root():
    return {"message": "LeadStream Real API", "version": "1.0.0"}

@app.post("/search", response_model=SearchResponse)
async def create_search(request: SearchRequest):
    """Create a new search for professionals"""
    try:
        # Create webset in Exa
        webset_data = await create_exa_webset(request.query)
        search_id = webset_data["id"]
        
        # Store search info
        searches[search_id] = {
            "query": request.query,
            "status": "processing",
            "created_at": datetime.now().isoformat(),
            "webset_data": webset_data
        }
        
        # Wait for completion
        max_attempts = 30
        for _ in range(max_attempts):
            status = await get_webset_status(search_id)
            if status.get("status") == "idle":
                searches[search_id]["status"] = "completed"
                break
            await asyncio.sleep(2)
        
        return SearchResponse(
            search_id=search_id,
            status="completed",
            message=f"Search completed for: {request.query}"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/results/{search_id}")
async def get_results(search_id: str):
    """Get real search results from Exa"""
    if search_id not in searches:
        raise HTTPException(status_code=404, detail="Search not found")
    
    try:
        # Get items from Exa
        items = await get_webset_items(search_id)
        
        # Format results
        formatted_results = []
        for item in items:
            properties = item.get("properties", {})
            person = properties.get("person", {})
            
            result = {
                "id": item.get("id"),
                "name": person.get("name", "Unknown"),
                "position": person.get("position", ""),
                "company": "", # Extract from position or description
                "location": person.get("location", ""),
                "url": properties.get("url", ""),
                "pictureUrl": person.get("pictureUrl", ""),
                "email": "",
                "phone": ""
            }
            
            # Try to extract company from position
            if person.get("position") and " at " in person.get("position"):
                parts = person.get("position").split(" at ")
                result["position"] = parts[0]
                result["company"] = parts[1] if len(parts) > 1 else ""
            
            formatted_results.append(result)
        
        results[search_id] = formatted_results
        return {"results": formatted_results, "query": searches[search_id]["query"]}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/enrich")
async def enrich_leads(request: EnrichRequest):
    """Enrich with contact info (placeholder for now)"""
    if request.search_id not in searches:
        raise HTTPException(status_code=404, detail="Search not found")
    
    # For now, just mark as enriched - real enrichment would call Exa enrichment API
    return {
        "status": "completed",
        "enriched_count": len(request.item_ids),
        "message": "Enrichment completed (contact info would be extracted from profiles)"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)