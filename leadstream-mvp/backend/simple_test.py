#!/usr/bin/env python3

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(title="Simple Test API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class SearchRequest(BaseModel):
    query: str

@app.get("/")
async def root():
    return {"message": "Simple Test API Working", "version": "1.0.0"}

@app.post("/search")
async def create_search(request: SearchRequest):
    print(f"Received search request: {request.query}")
    
    # Return test data with the webset ID we know works
    return {
        "search_id": "webset_cmbl0e0jx00f8it0i3npxczq6",
        "status": "completed",
        "message": f"Test search for: {request.query}"
    }

# Store results in memory for demo
demo_results = [
    {
        "id": "witem_01jx30ktq1t23yw7ztt23yw7zt",
        "name": "<PERSON>",
        "position": "Principal Software Engineer",
        "company": "City & County of San Francisco", 
        "location": "San Francisco Bay Area",
        "url": "https://www.linkedin.com/in/cathy-cruz-2850364",
        "pictureUrl": "https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2",
        "email": "",
        "phone": ""
    },
    {
        "id": "witem_01jx30kv70bkxwx7rbbkxwx7rb",
        "name": "Kevin Ly",
        "position": "Senior Software Engineer at New Relic",
        "company": "New Relic",
        "location": "San Francisco, California, United States", 
        "url": "https://www.linkedin.com/in/93lykevin",
        "pictureUrl": "https://media.licdn.com/dms/image/v2/C5603AQF-pHV6EyWHfA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1589579275285?e=2147483647&v=beta&t=TGOjO69pXOEtAc_4zFPYDCCRR2BT5C9oH_VSo-G34lQ",
        "email": "",
        "phone": ""
    },
    {
        "id": "witem_01jx30kw7b1mrcpe211mrcpe21",
        "name": "Laila Rizvi Zaidi",
        "position": "Staff Software Engineer at Meta",
        "company": "Meta",
        "location": "San Francisco, California, United States",
        "url": "https://www.linkedin.com/in/laila-rizvi-zaidi-53834b69", 
        "pictureUrl": "https://media.licdn.com/dms/image/v2/C5603AQGV3sPBrjORTA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1657319579857?e=2147483647&v=beta&t=FzQeXtbSevEA8EV80Mn0MWm7zl1mhfntjmnWnqHtsAM",
        "email": "",
        "phone": ""
    }
]

@app.get("/results/{search_id}")
async def get_results(search_id: str):
    return {"results": demo_results, "query": "software engineers in san francisco"}

class EnrichRequest(BaseModel):
    search_id: str
    item_ids: list

@app.post("/enrich")
async def enrich_leads(request: EnrichRequest):
    # Add demo contact info
    demo_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    demo_phones = ["******-555-0101", "******-555-0202", "******-555-0303"]
    
    for i, result in enumerate(demo_results):
        if result["id"] in request.item_ids:
            result["email"] = demo_emails[i % len(demo_emails)]
            result["phone"] = demo_phones[i % len(demo_phones)]
    
    return {
        "status": "completed",
        "enriched_count": len(request.item_ids),
        "message": "Demo enrichment completed with sample contact info"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)