# LeadStream Demo Instructions

## Setup (Before Demo)

1. **Add your Exa API key** to `backend/.env`:
   ```
   EXA_API_KEY=your_actual_api_key_here
   ```

2. **Start the backend**:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   uvicorn main:app --reload
   ```

3. **Start the frontend** (in new terminal):
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

4. **Verify both are running**:
   - Backend: http://localhost:8000 (should show API message)
   - Frontend: http://localhost:5173 (should show LeadStream interface)

## Demo Flow

### 1. Introduction (30 seconds)
"This is LeadStream, an AI-powered lead generation tool built with Exa Websets API. It demonstrates how we can use AI to find and enrich professional contacts automatically."

### 2. Search Demo (2 minutes)
- **Example search**: "Women entrepreneurs in California"
- Show the natural language input
- Explain how it creates an Exa webset behind the scenes
- Point out the real-time status updates
- Show results appearing in the table format

### 3. Enrichment Demo (1 minute)
- Select a few results using checkboxes
- Click "Enrich Selected"
- Show how emails and phone numbers get added
- Explain this is real contact information extracted from public sources

### 4. Export Demo (30 seconds)
- Click "Export CSV"
- Show the downloaded file with all data
- Mention this is ready for CRM import

### 5. Technical Highlights (1 minute)
- Built in one day using minimal tech stack
- FastAPI backend with single file
- React frontend with Tailwind CSS
- Demonstrates Exa's search and enrichment capabilities
- No authentication needed for demo purposes

## Backup Demo Queries
If the first search doesn't work well, try these:
- "CTOs at AI startups in San Francisco"
- "Marketing directors at e-commerce companies"
- "Founders of fintech companies in New York"

## Troubleshooting

**If search fails:**
- Check API key is correct in .env
- Verify backend is running on port 8000
- Check browser console for CORS errors

**If enrichment fails:**
- Enrichment takes time (up to 60 seconds)
- Some profiles may not have public contact info
- Try with different results

**If no results appear:**
- Try a different, more specific query
- Check that webset was created successfully at /docs endpoint

## Key Talking Points

1. **AI-First Approach**: Natural language queries instead of complex filters
2. **Real-Time Processing**: Shows status updates as search progresses
3. **Public Data Only**: Ethical data collection from publicly available sources
4. **Production Ready**: Could easily add authentication, database, etc.
5. **Scalable**: Built on modern async Python and React frameworks

This demo showcases how Exa Websets can power intelligent lead generation tools that save hours of manual research work.