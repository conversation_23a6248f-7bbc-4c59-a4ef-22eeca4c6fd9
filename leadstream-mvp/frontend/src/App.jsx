import { useState, useEffect } from 'react';
import { createSearch, checkSearchStatus, getResults, enrichItems } from './api';
import SearchBar from './components/SearchBar';
import ResultsTable from './components/ResultsTable';
import './App.css';

function App() {
  const [query, setQuery] = useState('');
  const [searchId, setSearchId] = useState(null);
  const [status, setStatus] = useState('idle'); // idle, searching, completed, error
  const [results, setResults] = useState([]);
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [enriching, setEnriching] = useState(false);
  const [error, setError] = useState(null);

  // Handle search completion
  useEffect(() => {
    if (!searchId || status !== 'searching') return;

    const fetchResults = async () => {
      try {
        // Since our simple backend returns completed immediately, fetch results right away
        const resultsData = await getResults(searchId);
        setResults(resultsData.results);
        setStatus('completed');
      } catch (err) {
        setError('Failed to fetch results');
        setStatus('error');
      }
    };

    // Small delay to show loading state
    const timer = setTimeout(fetchResults, 1000);
    return () => clearTimeout(timer);
  }, [searchId, status]);

  const handleSearch = async (searchQuery) => {
    try {
      setError(null);
      setQuery(searchQuery);
      setStatus('searching');
      setResults([]);
      setSelectedItems(new Set());
      
      console.log('Creating search for:', searchQuery);
      const searchData = await createSearch(searchQuery);
      console.log('Search response:', searchData);
      setSearchId(searchData.search_id);
    } catch (err) {
      console.error('Search error:', err);
      setError('Failed to create search');
      setStatus('error');
    }
  };

  const handleEnrich = async () => {
    if (selectedItems.size === 0) return;

    try {
      setEnriching(true);
      setError(null);
      
      const itemIds = Array.from(selectedItems);
      await enrichItems(searchId, itemIds);
      
      // Refresh results to show enriched data
      const resultsData = await getResults(searchId);
      setResults(resultsData.results);
      setSelectedItems(new Set());
    } catch (err) {
      setError('Failed to enrich items');
    } finally {
      setEnriching(false);
    }
  };

  const handleExport = () => {
    if (results.length === 0) return;

    // Convert results to CSV
    const headers = ['Name', 'Position', 'Company', 'Location', 'Email', 'Phone', 'LinkedIn URL'];
    const csvContent = [
      headers.join(','),
      ...results.map(r => [
        `"${r.name || ''}"`,
        `"${r.position || ''}"`,
        `"${r.company || ''}"`,
        `"${r.location || ''}"`,
        `"${r.email || ''}"`,
        `"${r.phone || ''}"`,
        `"${r.url || ''}"`
      ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `leadstream-results-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">LeadStream</h1>
          <p className="text-lg text-gray-600">Find and enrich professional contacts with AI</p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <SearchBar onSearch={handleSearch} isSearching={status === 'searching'} />

        {status === 'searching' && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <p className="mt-2 text-gray-600">Searching for professionals...</p>
          </div>
        )}

        {results.length > 0 && (
          <>
            <div className="mb-4 flex justify-between items-center">
              <p className="text-gray-700">Found {results.length} results for "{query}"</p>
              <div className="space-x-2">
                <button
                  onClick={handleEnrich}
                  disabled={selectedItems.size === 0 || enriching}
                  className={`px-4 py-2 rounded font-medium ${
                    selectedItems.size === 0 || enriching
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {enriching ? 'Enriching...' : `Enrich Selected (${selectedItems.size})`}
                </button>
                <button
                  onClick={handleExport}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 font-medium"
                >
                  Export CSV
                </button>
              </div>
            </div>
            <ResultsTable 
              results={results} 
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
            />
          </>
        )}
      </div>
    </div>
  );
}

export default App;