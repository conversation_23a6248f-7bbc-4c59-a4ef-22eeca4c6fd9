import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const createSearch = async (query) => {
  const response = await api.post('/search', { query });
  return response.data;
};

export const checkSearchStatus = async (searchId) => {
  const response = await api.get(`/search/${searchId}/status`);
  return response.data;
};

export const getResults = async (searchId) => {
  const response = await api.get(`/results/${searchId}`);
  return response.data;
};

export const enrichItems = async (searchId, itemIds) => {
  const response = await api.post('/enrich', {
    search_id: searchId,
    item_ids: itemIds,
  });
  return response.data;
};

export default api;