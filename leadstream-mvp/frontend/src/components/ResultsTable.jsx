function ResultsTable({ results, selectedItems, setSelectedItems }) {
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedItems(new Set(results.map(r => r.id)));
    } else {
      setSelectedItems(new Set());
    }
  };

  const handleSelectItem = (itemId) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const isAllSelected = results.length > 0 && selectedItems.size === results.length;

  return (
    <div className="overflow-x-auto shadow-sm rounded-lg">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-4 py-3 text-left">
              <input
                type="checkbox"
                checked={isAllSelected}
                onChange={handleSelectAll}
                className="rounded border-gray-300"
              />
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Name
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Position
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Company
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Location
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Email
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Phone
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Profile
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {results.map((result) => (
            <tr key={result.id} className="hover:bg-gray-50">
              <td className="px-4 py-3">
                <input
                  type="checkbox"
                  checked={selectedItems.has(result.id)}
                  onChange={() => handleSelectItem(result.id)}
                  className="rounded border-gray-300"
                />
              </td>
              <td className="px-4 py-3">
                <div className="flex items-center">
                  {result.pictureUrl && (
                    <img
                      src={result.pictureUrl}
                      alt={result.name}
                      className="h-10 w-10 rounded-full mr-3"
                      onError={(e) => { e.target.style.display = 'none' }}
                    />
                  )}
                  <div>
                    <div className="text-sm font-medium text-gray-900">{result.name}</div>
                  </div>
                </div>
              </td>
              <td className="px-4 py-3 text-sm text-gray-900">{result.position || '-'}</td>
              <td className="px-4 py-3 text-sm text-gray-900">{result.company || '-'}</td>
              <td className="px-4 py-3 text-sm text-gray-900">{result.location || '-'}</td>
              <td className="px-4 py-3 text-sm">
                {result.email ? (
                  <span className="text-green-600 font-medium">{result.email}</span>
                ) : (
                  <span className="text-gray-400">Not enriched</span>
                )}
              </td>
              <td className="px-4 py-3 text-sm">
                {result.phone ? (
                  <span className="text-green-600 font-medium">{result.phone}</span>
                ) : (
                  <span className="text-gray-400">Not enriched</span>
                )}
              </td>
              <td className="px-4 py-3 text-sm">
                {result.url && (
                  <a
                    href={result.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    View Profile
                  </a>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default ResultsTable;