import { useState } from 'react';

function SearchBar({ onSearch, isSearching }) {
  const [query, setQuery] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim() && !isSearching) {
      onSearch(query.trim());
    }
  };

  return (
    <form onSubmit={handleSubmit} className="mb-8">
      <div className="flex gap-2">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="e.g., Women entrepreneurs in California, CTOs at AI startups in San Francisco"
          className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
          disabled={isSearching}
        />
        <button
          type="submit"
          disabled={isSearching || !query.trim()}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isSearching || !query.trim()
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isSearching ? 'Searching...' : 'Search'}
        </button>
      </div>
      <p className="mt-2 text-sm text-gray-600">
        Enter a natural language query to find professionals matching your criteria
      </p>
    </form>
  );
}

export default SearchBar;