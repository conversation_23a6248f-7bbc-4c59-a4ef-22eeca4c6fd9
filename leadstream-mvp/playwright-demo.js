const { chromium } = require('playwright');

async function captureLeadStreamDemo() {
  const browser = await chromium.launch({ headless: false }); // Show browser
  const page = await browser.newPage();
  
  try {
    console.log('1. Opening LeadStream...');
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: 'screenshots/1-homepage.png', fullPage: true });
    
    console.log('2. Entering search query...');
    await page.fill('input[type="text"]', 'software engineers in san francisco');
    await page.screenshot({ path: 'screenshots/2-search-query.png', fullPage: true });
    
    console.log('3. Clicking search button...');
    await page.click('button:has-text("Search")');
    
    console.log('4. Waiting for results...');
    await page.waitForSelector('table', { timeout: 30000 });
    await page.waitForTimeout(2000); // Let results fully render
    await page.screenshot({ path: 'screenshots/3-search-results.png', fullPage: true });
    
    console.log('5. Selecting leads for enrichment...');
    // Click first two checkboxes
    const checkboxes = await page.locator('input[type="checkbox"]').all();
    if (checkboxes.length > 2) {
      await checkboxes[1].click(); // First result
      await checkboxes[2].click(); // Second result
    }
    await page.screenshot({ path: 'screenshots/4-selected-leads.png', fullPage: true });
    
    console.log('6. Enriching selected leads...');
    await page.click('button:has-text("Enrich Selected")');
    await page.waitForTimeout(3000); // Wait for enrichment
    await page.screenshot({ path: 'screenshots/5-enriched-results.png', fullPage: true });
    
    console.log('7. Exporting to CSV...');
    await page.click('button:has-text("Export CSV")');
    await page.screenshot({ path: 'screenshots/6-export-complete.png', fullPage: true });
    
    console.log('Demo complete! Screenshots saved in screenshots/ directory');
    
    // Keep browser open for 5 seconds to see the result
    await page.waitForTimeout(5000);
    
  } catch (error) {
    console.error('Error during demo:', error);
    await page.screenshot({ path: 'screenshots/error-state.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

// Create screenshots directory
const fs = require('fs');
if (!fs.existsSync('screenshots')) {
  fs.mkdirSync('screenshots');
}

// Run the demo
captureLeadStreamDemo();