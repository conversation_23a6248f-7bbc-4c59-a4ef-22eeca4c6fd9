const { chromium } = require('playwright');

(async () => {
  try {
    console.log('Launching browser...');
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    console.log('Navigating to localhost:5173...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
    
    console.log('Taking screenshot...');
    await page.screenshot({ 
      path: 'leadstream-screenshot.png',
      fullPage: true 
    });
    
    console.log('Screenshot saved as leadstream-screenshot.png');
    await browser.close();
  } catch (error) {
    console.error('Error taking screenshot:', error);
  }
})();