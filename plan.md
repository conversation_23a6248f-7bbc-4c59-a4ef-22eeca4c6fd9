# LeadStream MVP Development Plan

## Goal
Build a working demo in one day that showcases Exa Websets' ability to find and enrich professional leads.

## Timeline (8 hours)
- **Hour 1-2**: Backend API setup
- **Hour 3-4**: Frontend UI setup  
- **Hour 5-6**: Integration & core features
- **Hour 7**: Polish & bug fixes
- **Hour 8**: Demo preparation

## Core User Flow
1. User enters search criteria (e.g., "CTOs at AI startups in San Francisco")
2. <PERSON><PERSON> creates Exa webset and starts search
3. Results appear in real-time as they're found
4. User clicks "Enrich" to get emails/phones for selected leads
5. User can export results as CSV

## MVP Features (Must Have)
1. **Search Interface**: Single input field for natural language queries
2. **Results Table**: Display found professionals with basic info
3. **Enrichment**: One-click email/phone extraction
4. **Export**: Download results as CSV
5. **Status Indicators**: Show search/enrichment progress

## Not Included (Save Time)
- User accounts/authentication
- Saved searches
- Email verification
- Complex filters
- Database storage
- Production deployment

## API Endpoints Needed
1. `POST /search` - Create new Exa webset search
2. `GET /search/{id}/status` - Check search progress
3. `POST /enrich` - Enrich selected leads with contact info
4. `GET /results/{id}` - Get search results

## UI Components Needed
1. **SearchBar** - Input field with search button
2. **ResultsTable** - Display leads in table format
3. **StatusBadge** - Show processing status
4. **ExportButton** - Download CSV

## Development Order
1. Set up project structure
2. Create FastAPI backend with Exa integration
3. Build basic React UI
4. Connect frontend to backend
5. Add enrichment feature
6. Implement CSV export
7. Final testing

## Success Criteria
- Can search for "women entrepreneurs in California"
- Shows results similar to the LinkedIn screenshot
- Can enrich at least 10 leads with emails
- Looks professional enough for a demo
- Works reliably for the demo scenario