# LeadStream MVP Tasks Breakdown

## Task 1: Set up project structure (30 min)
- [ ] Create leadstream-mvp directory
- [ ] Create backend/ and frontend/ subdirectories  
- [ ] Initialize git repository
- [ ] Create .gitignore for Python and Node
- [ ] Create README.md with basic instructions

## Task 2: Create FastAPI backend (2 hours)

### 2.1 Basic Setup (30 min)
- [ ] Create backend/main.py
- [ ] Set up FastAPI app with CORS
- [ ] Create .env file for EXA_API_KEY
- [ ] Create requirements.txt
- [ ] Test server runs with uvicorn

### 2.2 Exa Integration (1 hour)
- [ ] Create `/search` endpoint that accepts query string
- [ ] Implement Exa webset creation with "person" entity type
- [ ] Add `/search/{id}/status` endpoint to check progress
- [ ] Add `/results/{id}` endpoint to get results
- [ ] Test with simple query like "CEOs in tech"

### 2.3 Enrichment Endpoint (30 min)
- [ ] Create `/enrich` endpoint that accepts item IDs
- [ ] Implement email/phone enrichment via Exa
- [ ] Return enriched data in consistent format

## Task 3: Build React UI (2 hours)

### 3.1 Project Setup (30 min)
- [ ] Initialize Vite React project
- [ ] Add Tailwind CSS via CDN to index.html
- [ ] Install axios
- [ ] Clean up boilerplate code

### 3.2 Core Components (1.5 hours)
- [ ] Create SearchBar component with input and button
- [ ] Create ResultsTable with columns: Name, Position, Company, Location, Email, Phone
- [ ] Add loading states and empty states
- [ ] Style with Tailwind classes

## Task 4: Frontend-Backend Integration (1.5 hours)

### 4.1 API Service (30 min)
- [ ] Create api.js with axios configuration
- [ ] Add functions: createSearch, checkStatus, getResults, enrichItems

### 4.2 Search Flow (1 hour)
- [ ] Wire up SearchBar to create webset on submit
- [ ] Poll status endpoint every 2 seconds
- [ ] Display results in table when ready
- [ ] Handle errors gracefully

## Task 5: Enrichment Feature (1 hour)
- [ ] Add checkboxes to select rows
- [ ] Add "Enrich Selected" button
- [ ] Call enrichment endpoint for selected items
- [ ] Update table with enriched data
- [ ] Show enrichment progress

## Task 6: CSV Export (30 min)
- [ ] Add "Export CSV" button
- [ ] Implement CSV generation in frontend
- [ ] Include all fields in export
- [ ] Auto-download file

## Task 7: Polish & Testing (1 hour)
- [ ] Test full flow with real query
- [ ] Fix any bugs
- [ ] Improve error messages
- [ ] Add simple instructions on homepage
- [ ] Ensure it works for demo scenario

## Simplified Command Reference

Backend:
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload
```

Frontend:
```bash
cd frontend
npm create vite@latest . -- --template react
npm install
npm install axios
npm run dev
```