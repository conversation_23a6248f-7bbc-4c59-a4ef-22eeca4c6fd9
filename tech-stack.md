# LeadStream MVP Tech Stack

## Why This Stack?

For a one-day MVP to showcase Exa Websets capabilities, I'm choosing the absolute minimum tools needed to get a working demo. No authentication, no database, no complex state management - just the essentials.

## Backend (Python)

### Single File FastAPI App
- **FastAPI** - Chose this because it gives us automatic API docs and async support out of the box
- **httpx** - For calling Exa API (supports async)
- **python-dotenv** - To manage API keys

That's it. No database, no Redis, no Celery. Just a simple API that forwards requests to Exa.

### Backend Dependencies (requirements.txt)
```
fastapi==0.104.1
uvicorn==0.24.0
httpx==0.25.2
python-dotenv==1.0.0
```

## Frontend (Simple React)

### Minimal React Setup
- **Vite** - Fastest way to get React running
- **React** - For UI components
- **Axios** - Simple HTTP client
- **Tailwind CSS** - Quick styling via CDN

No TypeScript, no complex state management, no component libraries. Just vanilla React with <PERSON><PERSON>wind for quick styling.

### Frontend Dependencies (package.json)
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.6.2"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.2.0",
    "vite": "^5.0.0"
  }
}
```

## Project Structure (KISS)

```
leadstream-mvp/
├── backend/
│   ├── main.py          # Single file FastAPI app
│   ├── .env             # API keys
│   └── requirements.txt
│
├── frontend/
│   ├── index.html
│   ├── src/
│   │   ├── App.jsx      # Single component app
│   │   └── main.jsx
│   └── package.json
│
└── README.md
```

## Why These Choices?

1. **FastAPI over Flask**: Auto API docs save time, async support for Exa calls
2. **No Database**: Results stored in frontend state only
3. **No Auth**: Open access for demo purposes
4. **Vite over CRA**: Faster startup, less config
5. **Plain CSS/Tailwind CDN**: No build step for styles
6. **Single File Backend**: Everything in main.py for simplicity
7. **No Docker**: Just run locally with uvicorn and npm

## Quick Start Commands

Backend:
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

Frontend:
```bash
cd frontend
npm install
npm run dev
```

That's it. API runs on :8000, frontend on :5173.