# TechWellness Agent

A professional discovery and contact enrichment application using the Exa Websets API.

## Project Structure

```
techwellness-agent/
├── frontend/          # React frontend application
├── backend/           # Node.js backend API
└── README.md         # This file
```

## Quick Start

### 1. Clone and Setup
```bash
# Navigate to the project directory
cd techwellness-agent

# Install backend dependencies
cd backend
npm install
cd ..

# Install frontend dependencies
cd frontend
npm install
cd ..
```

### 2. Environment Configuration

#### Backend Environment
Create `backend/.env`:
```bash
EXA_API_KEY=your_exa_api_key_here
PORT=3001
NODE_ENV=development
```

#### Frontend Environment
Create `frontend/.env`:
```bash
VITE_API_URL=http://localhost:3001
```

### 3. Start the Application

#### Terminal 1 - Backend
```bash
cd backend
node server.js
```

#### Terminal 2 - Frontend
```bash
cd frontend
npm run dev
```

### 4. Access the Application
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001
- Health Check: http://localhost:3001/health

## Environment Variables

Create `.env` files in both frontend and backend directories:

### Frontend (.env)
```
VITE_API_URL=http://localhost:3001
```

### Backend (.env)
```
EXA_API_KEY=your_exa_api_key_here
PORT=3001
```

## Features

- Professional discovery by role, industry, and location
- Real-time search progress tracking
- Contact information enrichment
- CSV export functionality
- Clean, responsive UI

## Tech Stack

### Frontend
- React 18
- Vite
- Tailwind CSS
- Axios

### Backend
- Node.js
- Express
- Exa Websets API
- CORS

## API Endpoints

### POST /api/search
Search for professionals by query.

**Request:**
```json
{
  "query": "software engineers in San Francisco"
}
```

**Response:**
```json
{
  "success": true,
  "query": "software engineers in San Francisco",
  "results": [
    {
      "id": "prof_123",
      "name": "John Doe",
      "title": "Senior Software Engineer",
      "company": "TechCorp Inc",
      "location": "San Francisco, CA",
      "linkedin": "https://linkedin.com/in/johndoe",
      "email": null,
      "phone": null
    }
  ],
  "count": 1
}
```

### POST /api/enrich
Enrich contact information for a profile.

**Request:**
```json
{
  "profileId": "prof_123"
}
```

**Response:**
```json
{
  "success": true,
  "profileId": "prof_123",
  "email": "<EMAIL>",
  "phone": "+****************"
}
```

## Usage

1. **Search**: Enter a query like "software engineers in San Francisco" or "marketing managers at tech companies"
2. **Review Results**: Browse the discovered professionals with their basic information
3. **Enrich Contacts**: Click "Enrich Contact" to get email and phone information
4. **Export**: Use the "Export CSV" button to download the results

## Development Notes

- The application uses mock data when the Exa API is not available
- Contact enrichment is currently simulated with mock data
- The frontend automatically handles API errors and displays user-friendly messages
- Both frontend and backend have hot-reload enabled for development

## Troubleshooting

### Backend Issues
- Ensure you're in the `backend` directory when running `node server.js`
- Check that port 3001 is not in use by another application
- Verify your `.env` file is properly configured

### Frontend Issues
- Ensure you're in the `frontend` directory when running `npm run dev`
- Check that the backend is running on port 3001
- Verify the `VITE_API_URL` in your `.env` file

### API Issues
- The application will use mock data if the Exa API key is not configured
- Check the browser console for detailed error messages
- Verify your Exa API key is valid and has sufficient credits

This is a proof-of-concept application built for rapid professional discovery and networking.
