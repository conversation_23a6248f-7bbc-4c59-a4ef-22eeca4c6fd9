# TechWellness Agent Backend

Node.js/Express backend API for the TechWellness Agent application.

## Features

- Professional search using Exa Websets API
- Contact information enrichment
- RESTful API endpoints
- Error handling and logging
- CORS support for frontend integration

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create `.env` file:
```bash
cp .env.example .env
```

3. Add your Exa API key to `.env`:
```
EXA_API_KEY=your_actual_api_key_here
PORT=3001
NODE_ENV=development
```

## Development

Start the development server:
```bash
npm run dev
```

The server will run on http://localhost:3001

## API Endpoints

### POST /api/search
Search for professionals by query.

**Request:**
```json
{
  "query": "software engineers in San Francisco"
}
```

**Response:**
```json
{
  "success": true,
  "query": "software engineers in San Francisco",
  "results": [...],
  "count": 10
}
```

### POST /api/enrich
Enrich contact information for a profile.

**Request:**
```json
{
  "profileId": "prof_123"
}
```

**Response:**
```json
{
  "success": true,
  "profileId": "prof_123",
  "email": "<EMAIL>",
  "phone": "+****************"
}
```

### GET /health
Health check endpoint.

## Project Structure

```
backend/
├── routes/          # API route handlers
├── services/        # Business logic and external API integration
├── server.js        # Main application entry point
├── package.json     # Dependencies and scripts
└── .env            # Environment variables
```
