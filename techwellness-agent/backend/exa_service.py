#!/usr/bin/env python3
"""
Python service for Exa API integration
This script can be called from Node.js to perform Exa searches
"""

import sys
import json
import os
from dotenv import load_dotenv
from exa_py import Exa

# Load environment variables
load_dotenv()

def search_professionals(query, num_results=10):
    """Search for professionals using Exa API"""
    
    api_key = os.getenv('EXA_API_KEY')
    if not api_key or api_key == 'your_exa_api_key_here':
        raise Exception("EXA_API_KEY not configured")
    
    try:
        exa = Exa(api_key)
        
        # Enhance query for better LinkedIn results
        enhanced_query = f"{query} site:linkedin.com/in"
        
        search_response = exa.search(
            query=enhanced_query,
            num_results=num_results,
            include_domains=["linkedin.com"],
            type="neural"
        )

        # Get content for the results
        if search_response.results:
            urls = [result.url for result in search_response.results]
            content_response = exa.get_contents(urls)

            # Combine search results with content
            for i, result in enumerate(search_response.results):
                if i < len(content_response.results):
                    result.text = content_response.results[i].text
        
        professionals = []
        for i, result in enumerate(search_response.results):
            # Extract information from LinkedIn profiles
            professional = {
                "id": f"prof_exa_{i}_{hash(result.url) % 10000}",
                "name": extract_name_from_title(result.title),
                "title": extract_title_from_text(result.text),
                "company": extract_company_from_text(result.text),
                "location": extract_location_from_text(result.text),
                "linkedin": result.url,
                "email": None,
                "phone": None,
                "source": "exa_api"
            }
            professionals.append(professional)
        
        return professionals
        
    except Exception as e:
        raise Exception(f"Exa API error: {str(e)}")

def extract_name_from_title(title):
    """Extract name from LinkedIn title"""
    if not title:
        return "Unknown"
    
    # LinkedIn titles often start with the person's name
    parts = title.split(" on LinkedIn")
    if parts:
        name_part = parts[0].strip()
        # Remove common prefixes
        name_part = name_part.replace("Dr. ", "").replace("Mr. ", "").replace("Ms. ", "")
        return name_part if len(name_part) > 0 else "Unknown"
    
    return "Unknown"

def extract_title_from_text(text):
    """Extract job title from text"""
    if not text:
        return "Professional"
    
    # Look for common job title patterns
    title_keywords = [
        "Engineer", "Developer", "Manager", "Director", "VP", "CEO", "CTO", "CFO",
        "Analyst", "Specialist", "Consultant", "Lead", "Senior", "Principal",
        "Architect", "Designer", "Product Manager", "Marketing", "Sales"
    ]
    
    text_lower = text.lower()
    for keyword in title_keywords:
        if keyword.lower() in text_lower:
            # Try to extract the full title around the keyword
            sentences = text.split('.')
            for sentence in sentences:
                if keyword.lower() in sentence.lower():
                    # Clean and return the sentence as title
                    title = sentence.strip()
                    if len(title) < 100:  # Reasonable title length
                        return title
            return keyword
    
    return "Professional"

def extract_company_from_text(text):
    """Extract company from text"""
    if not text:
        return "Unknown Company"
    
    # Look for company indicators
    company_patterns = [
        " at ", " @ ", "works at", "employed at", "company:"
    ]
    
    text_lower = text.lower()
    for pattern in company_patterns:
        if pattern in text_lower:
            # Try to extract company name after the pattern
            parts = text_lower.split(pattern)
            if len(parts) > 1:
                company_part = parts[1].split('.')[0].split(',')[0].strip()
                if len(company_part) < 50:  # Reasonable company name length
                    return company_part.title()
    
    return "Unknown Company"

def extract_location_from_text(text):
    """Extract location from text"""
    if not text:
        return "Unknown Location"
    
    # Look for location patterns
    location_patterns = [
        "based in", "located in", "from", "lives in", "area:"
    ]
    
    text_lower = text.lower()
    for pattern in location_patterns:
        if pattern in text_lower:
            parts = text_lower.split(pattern)
            if len(parts) > 1:
                location_part = parts[1].split('.')[0].split(',')[0].strip()
                if len(location_part) < 30:  # Reasonable location length
                    return location_part.title()
    
    return "Unknown Location"

def main():
    """Main function for command line usage"""
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: python exa_service.py '<search_query>'"}))
        sys.exit(1)
    
    query = sys.argv[1]
    
    try:
        results = search_professionals(query)
        print(json.dumps({
            "success": True,
            "results": results,
            "count": len(results)
        }))
    except Exception as e:
        print(json.dumps({
            "success": False,
            "error": str(e)
        }))
        sys.exit(1)

if __name__ == "__main__":
    main()
