{"name": "es-errors", "version": "1.3.0", "description": "A simple cache for a few of the JS Error constructors.", "main": "index.js", "exports": {".": "./index.js", "./eval": "./eval.js", "./range": "./range.js", "./ref": "./ref.js", "./syntax": "./syntax.js", "./type": "./type.js", "./uri": "./uri.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "homepage": "https://github.com/ljharb/es-errors#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}