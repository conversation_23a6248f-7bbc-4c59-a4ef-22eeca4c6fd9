{"name": "get-proto", "version": "1.0.1", "description": "Robustly get the [[Prototype]] of an object", "main": "index.js", "exports": {".": "./index.js", "./Reflect.getPrototypeOf": "./Reflect.getPrototypeOf.js", "./Object.getPrototypeOf": "./Object.getPrototypeOf.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "npx npm@\">=10.2\" audit --production", "tests-only": "nyc tape 'test/**/*.js'", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-proto.git"}, "keywords": ["get", "proto", "prototype", "getPrototypeOf", "[[Prototype]]"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/get-proto/issues"}, "homepage": "https://github.com/ljharb/get-proto#readme", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "testling": {"files": "test/index.js"}}