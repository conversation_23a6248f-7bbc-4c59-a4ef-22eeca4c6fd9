const express = require('express')
const { enrichContactInfo } = require('../services/exaService')

const router = express.Router()

// POST /api/enrich - Enrich contact information
router.post('/enrich', async (req, res) => {
  try {
    const { profileId } = req.body

    if (!profileId) {
      return res.status(400).json({ error: 'Profile ID is required' })
    }

    console.log(`Enriching contact info for profile: ${profileId}`)
    
    const enrichedData = await enrichContactInfo(profileId)
    
    res.json({
      success: true,
      profileId,
      ...enrichedData
    })

  } catch (error) {
    console.error('Enrichment error:', error)
    res.status(500).json({ 
      error: error.message || 'Failed to enrich contact information',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
  }
})

module.exports = router
