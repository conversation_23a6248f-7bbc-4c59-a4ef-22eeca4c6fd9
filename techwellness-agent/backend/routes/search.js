const express = require('express')
const { searchProfessionals, getSearchStatus } = require('../services/exaService')

const router = express.Router()

// POST /api/search - Create a new search
router.post('/search', async (req, res) => {
  try {
    const { query } = req.body

    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Query is required and must be a string' })
    }

    console.log(`Starting search for: "${query}"`)
    
    const results = await searchProfessionals(query)
    
    res.json({
      success: true,
      query,
      results,
      count: results.length
    })

  } catch (error) {
    console.error('Search error:', error)
    res.status(500).json({ 
      error: error.message || 'Failed to search professionals',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
  }
})

// GET /api/search/:searchId/status - Get search status
router.get('/search/:searchId/status', async (req, res) => {
  try {
    const { searchId } = req.params
    
    const status = await getSearchStatus(searchId)
    
    res.json(status)

  } catch (error) {
    console.error('Status check error:', error)
    res.status(500).json({ 
      error: error.message || 'Failed to check search status',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
  }
})

module.exports = router
