const axios = require('axios')

const EXA_API_BASE = 'https://api.exa.ai'
const EXA_API_KEY = process.env.EXA_API_KEY

if (!EXA_API_KEY) {
  console.warn('Warning: EXA_API_KEY not found in environment variables')
}

const exaApi = axios.create({
  baseURL: EXA_API_BASE,
  headers: {
    'Authorization': `Bearer ${EXA_API_KEY}`,
    'Content-Type': 'application/json'
  },
  timeout: 30000
})

// Search for professionals using Exa Websets API
const searchProfessionals = async (query) => {
  try {
    console.log('Creating Exa webset for query:', query)
    
    // Step 1: Create a webset
    const websetResponse = await exaApi.post('/websets', {
      query: query,
      num_results: 20,
      include_domains: ['linkedin.com'],
      type: 'neural'
    })

    const websetId = websetResponse.data.id
    console.log('Created webset:', websetId)

    // Step 2: Poll for completion
    let status = 'pending'
    let attempts = 0
    const maxAttempts = 30 // 5 minutes max

    while (status === 'pending' && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 10000)) // Wait 10 seconds
      
      const statusResponse = await exaApi.get(`/websets/${websetId}`)
      status = statusResponse.data.status
      attempts++
      
      console.log(`Webset ${websetId} status: ${status} (attempt ${attempts})`)
    }

    if (status !== 'completed') {
      throw new Error(`Search did not complete. Final status: ${status}`)
    }

    // Step 3: Get results
    const resultsResponse = await exaApi.get(`/websets/${websetId}/results`)
    const rawResults = resultsResponse.data.results || []

    // Step 4: Transform results into our format
    const professionals = rawResults.map((result, index) => ({
      id: `prof_${websetId}_${index}`,
      name: extractNameFromTitle(result.title),
      title: extractTitleFromContent(result.text),
      company: extractCompanyFromContent(result.text),
      location: extractLocationFromContent(result.text),
      linkedin: result.url,
      email: null, // Will be enriched later
      phone: null, // Will be enriched later
      source: 'exa_webset',
      websetId: websetId
    }))

    console.log(`Found ${professionals.length} professionals`)
    return professionals

  } catch (error) {
    console.error('Exa API error:', error.response?.data || error.message)
    
    // Return mock data for development if API fails
    if (process.env.NODE_ENV === 'development') {
      console.log('Returning mock data for development')
      return getMockProfessionals(query)
    }
    
    throw new Error(`Failed to search professionals: ${error.message}`)
  }
}

// Get search status
const getSearchStatus = async (searchId) => {
  try {
    const response = await exaApi.get(`/websets/${searchId}`)
    return {
      id: searchId,
      status: response.data.status,
      progress: response.data.progress || 0
    }
  } catch (error) {
    console.error('Status check error:', error.response?.data || error.message)
    throw new Error(`Failed to check status: ${error.message}`)
  }
}

// Enrich contact information (mock implementation)
const enrichContactInfo = async (profileId) => {
  try {
    // In a real implementation, this would use a contact enrichment service
    // For now, we'll return mock enriched data
    
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay
    
    const mockEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]
    
    const mockPhones = [
      '+****************',
      '+****************',
      '+****************'
    ]
    
    return {
      email: mockEmails[Math.floor(Math.random() * mockEmails.length)],
      phone: mockPhones[Math.floor(Math.random() * mockPhones.length)]
    }
    
  } catch (error) {
    console.error('Contact enrichment error:', error)
    throw new Error(`Failed to enrich contact: ${error.message}`)
  }
}

// Helper functions for data extraction
const extractNameFromTitle = (title) => {
  if (!title) return 'Unknown'
  
  // Try to extract name from LinkedIn title format
  const match = title.match(/^([^|]+)/)
  return match ? match[1].trim() : title.split(' ').slice(0, 2).join(' ')
}

const extractTitleFromContent = (text) => {
  if (!text) return 'Professional'
  
  // Look for job titles in common formats
  const titlePatterns = [
    /(?:Senior|Lead|Principal|Director|Manager|Engineer|Developer|Analyst|Specialist)\s+[A-Za-z\s]+/i,
    /[A-Za-z\s]+(?:Engineer|Developer|Manager|Director|Analyst|Specialist)/i
  ]
  
  for (const pattern of titlePatterns) {
    const match = text.match(pattern)
    if (match) return match[0].trim()
  }
  
  return 'Professional'
}

const extractCompanyFromContent = (text) => {
  if (!text) return 'Unknown Company'
  
  // Look for company indicators
  const companyPatterns = [
    /at\s+([A-Z][A-Za-z\s&]+(?:Inc|LLC|Corp|Ltd)?)/,
    /works?\s+(?:at|for)\s+([A-Z][A-Za-z\s&]+)/i
  ]
  
  for (const pattern of companyPatterns) {
    const match = text.match(pattern)
    if (match) return match[1].trim()
  }
  
  return 'Unknown Company'
}

const extractLocationFromContent = (text) => {
  if (!text) return 'Unknown Location'
  
  // Look for location patterns
  const locationPatterns = [
    /(?:in|from|based in)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:,\s*[A-Z]{2})?)/,
    /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*,\s*[A-Z]{2})/
  ]
  
  for (const pattern of locationPatterns) {
    const match = text.match(pattern)
    if (match) return match[1].trim()
  }
  
  return 'Unknown Location'
}

// Mock data for development
const getMockProfessionals = (query) => {
  const mockData = [
    {
      id: 'prof_mock_1',
      name: 'John Doe',
      title: 'Senior Software Engineer',
      company: 'TechCorp Inc',
      location: 'San Francisco, CA',
      linkedin: 'https://linkedin.com/in/johndoe',
      email: null,
      phone: null,
      source: 'mock'
    },
    {
      id: 'prof_mock_2',
      name: 'Jane Smith',
      title: 'Product Manager',
      company: 'StartupXYZ',
      location: 'New York, NY',
      linkedin: 'https://linkedin.com/in/janesmith',
      email: null,
      phone: null,
      source: 'mock'
    },
    {
      id: 'prof_mock_3',
      name: 'Alex Johnson',
      title: 'Marketing Director',
      company: 'Growth Co',
      location: 'Austin, TX',
      linkedin: 'https://linkedin.com/in/alexjohnson',
      email: null,
      phone: null,
      source: 'mock'
    }
  ]
  
  return mockData.filter(prof => 
    prof.title.toLowerCase().includes(query.toLowerCase()) ||
    prof.company.toLowerCase().includes(query.toLowerCase())
  )
}

module.exports = {
  searchProfessionals,
  getSearchStatus,
  enrichContactInfo
}
