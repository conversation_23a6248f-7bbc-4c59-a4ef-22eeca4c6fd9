const { spawn } = require('child_process')
const path = require('path')

// Search for professionals using Python Exa service
const searchProfessionals = async (query) => {
  try {
    console.log('Searching for professionals with query:', query)

    // Call Python script
    const pythonScript = path.join(__dirname, '..', 'exa_service.py')
    const result = await callPythonScript(pythonScript, [query])

    if (result.success) {
      console.log(`Found ${result.count} professionals via Exa API`)
      return result.results
    } else {
      throw new Error(result.error)
    }

  } catch (error) {
    console.error('Exa service error:', error.message)

    // Return mock data for development if API fails
    if (process.env.NODE_ENV === 'development') {
      console.log('Returning mock data for development')
      return getMockProfessionals(query)
    }

    throw new Error(`Failed to search professionals: ${error.message}`)
  }
}

// Helper function to call Python script
const callPythonScript = (scriptPath, args) => {
  return new Promise((resolve, reject) => {
    const python = spawn('python3', [scriptPath, ...args])
    let stdout = ''
    let stderr = ''

    python.stdout.on('data', (data) => {
      stdout += data.toString()
    })

    python.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    python.on('close', (code) => {
      if (code === 0) {
        try {
          const result = JSON.parse(stdout)
          resolve(result)
        } catch (parseError) {
          reject(new Error(`Failed to parse Python output: ${parseError.message}`))
        }
      } else {
        reject(new Error(`Python script failed with code ${code}: ${stderr}`))
      }
    })

    python.on('error', (error) => {
      reject(new Error(`Failed to start Python script: ${error.message}`))
    })
  })
}

// Get search status
const getSearchStatus = async (searchId) => {
  try {
    const response = await exaApi.get(`/websets/${searchId}`)
    return {
      id: searchId,
      status: response.data.status,
      progress: response.data.progress || 0
    }
  } catch (error) {
    console.error('Status check error:', error.response?.data || error.message)
    throw new Error(`Failed to check status: ${error.message}`)
  }
}

// Enrich contact information (mock implementation)
const enrichContactInfo = async (profileId) => {
  try {
    // In a real implementation, this would use a contact enrichment service
    // For now, we'll return mock enriched data
    
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay
    
    const mockEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]
    
    const mockPhones = [
      '+****************',
      '+****************',
      '+****************'
    ]
    
    return {
      email: mockEmails[Math.floor(Math.random() * mockEmails.length)],
      phone: mockPhones[Math.floor(Math.random() * mockPhones.length)]
    }
    
  } catch (error) {
    console.error('Contact enrichment error:', error)
    throw new Error(`Failed to enrich contact: ${error.message}`)
  }
}



// Mock data for development
const getMockProfessionals = (query) => {
  const mockData = [
    {
      id: 'prof_mock_1',
      name: 'John Doe',
      title: 'Senior Software Engineer',
      company: 'TechCorp Inc',
      location: 'San Francisco, CA',
      linkedin: 'https://linkedin.com/in/johndoe',
      email: null,
      phone: null,
      source: 'mock'
    },
    {
      id: 'prof_mock_2',
      name: 'Jane Smith',
      title: 'Product Manager',
      company: 'StartupXYZ',
      location: 'New York, NY',
      linkedin: 'https://linkedin.com/in/janesmith',
      email: null,
      phone: null,
      source: 'mock'
    },
    {
      id: 'prof_mock_3',
      name: 'Alex Johnson',
      title: 'Marketing Director',
      company: 'Growth Co',
      location: 'Austin, TX',
      linkedin: 'https://linkedin.com/in/alexjohnson',
      email: null,
      phone: null,
      source: 'mock'
    }
  ]
  
  return mockData.filter(prof => 
    prof.title.toLowerCase().includes(query.toLowerCase()) ||
    prof.company.toLowerCase().includes(query.toLowerCase())
  )
}

module.exports = {
  searchProfessionals,
  getSearchStatus,
  enrichContactInfo
}
