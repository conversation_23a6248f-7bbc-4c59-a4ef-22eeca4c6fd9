#!/usr/bin/env node

/**
 * Demo script to test the TechWellness Agent API
 * Run with: node demo-test.js
 */

const axios = require('axios')

const API_BASE = 'http://localhost:3001'

async function testHealthCheck() {
  console.log('🔍 Testing health check...')
  try {
    const response = await axios.get(`${API_BASE}/health`)
    console.log('✅ Health check passed:', response.data)
    return true
  } catch (error) {
    console.log('❌ Health check failed:', error.message)
    return false
  }
}

async function testSearch() {
  console.log('\n🔍 Testing professional search...')
  try {
    const response = await axios.post(`${API_BASE}/api/search`, {
      query: 'software engineers in San Francisco'
    })
    
    console.log('✅ Search successful!')
    console.log(`📊 Found ${response.data.count} professionals`)
    
    if (response.data.results.length > 0) {
      const firstResult = response.data.results[0]
      console.log('👤 First result:', {
        name: firstResult.name,
        title: firstResult.title,
        company: firstResult.company,
        location: firstResult.location
      })
      return firstResult.id
    }
    
    return null
  } catch (error) {
    console.log('❌ Search failed:', error.response?.data || error.message)
    return null
  }
}

async function testEnrichment(profileId) {
  if (!profileId) {
    console.log('\n⚠️  Skipping enrichment test - no profile ID available')
    return
  }
  
  console.log('\n🔍 Testing contact enrichment...')
  try {
    const response = await axios.post(`${API_BASE}/api/enrich`, {
      profileId: profileId
    })
    
    console.log('✅ Enrichment successful!')
    console.log('📧 Contact info:', {
      email: response.data.email,
      phone: response.data.phone
    })
  } catch (error) {
    console.log('❌ Enrichment failed:', error.response?.data || error.message)
  }
}

async function runDemo() {
  console.log('🚀 TechWellness Agent API Demo\n')
  
  // Test health check
  const healthOk = await testHealthCheck()
  if (!healthOk) {
    console.log('\n❌ Backend is not running. Please start it with: cd backend && node server.js')
    process.exit(1)
  }
  
  // Test search
  const profileId = await testSearch()
  
  // Test enrichment
  await testEnrichment(profileId)
  
  console.log('\n🎉 Demo completed!')
  console.log('\n📝 Next steps:')
  console.log('1. Open http://localhost:5173 in your browser')
  console.log('2. Try searching for professionals')
  console.log('3. Test the enrichment feature')
  console.log('4. Export results as CSV')
}

// Run the demo
runDemo().catch(console.error)
