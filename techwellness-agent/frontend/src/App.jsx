import { useState } from 'react'
import SearchBar from './components/SearchBar'
import ResultsTable from './components/ResultsTable'
import LoadingIndicator from './components/LoadingIndicator'
import { searchProfessionals, enrichContact } from './api/api'

function App() {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState([])
  const [loading, setLoading] = useState(false)
  const [selectedItems, setSelectedItems] = useState(new Set())
  const [error, setError] = useState('')

  const handleSearch = async (query) => {
    setLoading(true)
    setError('')
    setSearchResults([])
    setSelectedItems(new Set())

    try {
      const results = await searchProfessionals(query)
      setSearchResults(results)
    } catch (err) {
      setError(err.message || 'An error occurred during search')
    } finally {
      setLoading(false)
    }
  }

  const handleEnrichContact = async (profileId) => {
    try {
      const enrichedData = await enrichContact(profileId)
      setSearchResults(prev =>
        prev.map(result =>
          result.id === profileId
            ? { ...result, ...enrichedData }
            : result
        )
      )
    } catch (err) {
      setError(err.message || 'Failed to enrich contact')
    }
  }

  const handleExportCSV = () => {
    if (searchResults.length === 0) return

    const headers = ['Name', 'Title', 'Company', 'Location', 'Email', 'Phone', 'LinkedIn']
    const csvContent = [
      headers.join(','),
      ...searchResults.map(result => [
        result.name || '',
        result.title || '',
        result.company || '',
        result.location || '',
        result.email || '',
        result.phone || '',
        result.linkedin || ''
      ].map(field => `"${field}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `professionals_${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="min-h-screen">
      <div className="container py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            TechWellness Agent
          </h1>
          <p className="text-lg text-gray-600">
            Discover professionals and enrich contact information
          </p>
        </header>

        <div style={{maxWidth: '1200px', margin: '0 auto'}}>
          <SearchBar
            onSearch={handleSearch}
            loading={loading}
          />

          {error && (
            <div className="error">
              {error}
            </div>
          )}

          {loading && <LoadingIndicator />}

          {searchResults.length > 0 && (
            <div className="mt-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Search Results ({searchResults.length})
                </h2>
                <button
                  onClick={handleExportCSV}
                  className="btn btn-success"
                >
                  Export CSV
                </button>
              </div>

              <ResultsTable
                results={searchResults}
                selectedItems={selectedItems}
                onSelectItem={setSelectedItems}
                onEnrichContact={handleEnrichContact}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
