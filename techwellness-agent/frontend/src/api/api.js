import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
})

// Search for professionals using Exa Websets API
export const searchProfessionals = async (query) => {
  try {
    const response = await api.post('/api/search', { query })
    return response.data.results || []
  } catch (error) {
    console.error('Search error:', error)
    throw new Error(error.response?.data?.error || 'Failed to search professionals')
  }
}

// Enrich contact information for a specific profile
export const enrichContact = async (profileId) => {
  try {
    const response = await api.post('/api/enrich', { profileId })
    return response.data
  } catch (error) {
    console.error('Enrichment error:', error)
    throw new Error(error.response?.data?.error || 'Failed to enrich contact information')
  }
}

// Get search status (for polling)
export const getSearchStatus = async (searchId) => {
  try {
    const response = await api.get(`/api/search/${searchId}/status`)
    return response.data
  } catch (error) {
    console.error('Status check error:', error)
    throw new Error(error.response?.data?.error || 'Failed to check search status')
  }
}

export default api
