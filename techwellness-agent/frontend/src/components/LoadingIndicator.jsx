const LoadingIndicator = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-8 text-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">Searching for professionals...</h3>
          <p className="text-sm text-gray-500">
            This may take a few moments as we search across professional networks
          </p>
        </div>
        <div className="w-full max-w-md bg-gray-200 rounded-full h-2">
          <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
        </div>
      </div>
    </div>
  )
}

export default LoadingIndicator
