const ResultsTable = ({ results, selectedItems, onSelectItem, onEnrichContact }) => {
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      onSelectItem(new Set(results.map(r => r.id)))
    } else {
      onSelectItem(new Set())
    }
  }

  const handleSelectItem = (id) => {
    const newSelected = new Set(selectedItems)
    if (newSelected.has(id)) {
      newSelected.delete(id)
    } else {
      newSelected.add(id)
    }
    onSelectItem(newSelected)
  }

  return (
    <div className="card" style={{padding: 0}}>
      <div style={{overflowX: 'auto'}}>
        <table className="table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.size === results.length && results.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th>Name</th>
              <th>Title</th>
              <th>Company</th>
              <th>Location</th>
              <th>Contact</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {results.map((result) => (
              <tr key={result.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.has(result.id)}
                    onChange={() => handleSelectItem(result.id)}
                  />
                </td>
                <td>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {result.name || 'N/A'}
                    </div>
                    {result.linkedin && (
                      <a
                        href={result.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        LinkedIn Profile
                      </a>
                    )}
                  </div>
                </td>
                <td>
                  <div className="text-sm text-gray-900">{result.title || 'N/A'}</div>
                </td>
                <td>
                  <div className="text-sm text-gray-900">{result.company || 'N/A'}</div>
                </td>
                <td>
                  <div className="text-sm text-gray-900">{result.location || 'N/A'}</div>
                </td>
                <td>
                  <div className="text-sm text-gray-900">
                    {result.email && (
                      <div>
                        <a href={`mailto:${result.email}`} className="text-blue-600 hover:text-blue-800">
                          {result.email}
                        </a>
                      </div>
                    )}
                    {result.phone && (
                      <div className="text-gray-600">{result.phone}</div>
                    )}
                    {!result.email && !result.phone && (
                      <span className="text-gray-400">Not enriched</span>
                    )}
                  </div>
                </td>
                <td>
                  {!result.email && !result.phone && (
                    <button
                      onClick={() => onEnrichContact(result.id)}
                      className="text-blue-600 hover:text-blue-900"
                      style={{background: 'none', border: 'none', cursor: 'pointer'}}
                    >
                      Enrich Contact
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {results.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No results found. Try a different search query.
        </div>
      )}
    </div>
  )
}

export default ResultsTable
