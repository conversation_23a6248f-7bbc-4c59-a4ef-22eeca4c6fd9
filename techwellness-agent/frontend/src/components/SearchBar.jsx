import { useState } from 'react'

const SearchBar = ({ onSearch, loading }) => {
  const [query, setQuery] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    if (query.trim() && !loading) {
      onSearch(query.trim())
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
            Search for Professionals
          </label>
          <input
            type="text"
            id="search"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="e.g., software engineers in San Francisco, marketing managers at tech companies"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>
        
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-500">
            Enter a description of the professionals you're looking for
          </p>
          <button
            type="submit"
            disabled={loading || !query.trim()}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </form>
      
      <div className="mt-4 text-xs text-gray-400">
        <p>Examples:</p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>"Product managers at fintech startups"</li>
          <li>"Senior developers in New York"</li>
          <li>"Marketing directors at SaaS companies"</li>
        </ul>
      </div>
    </div>
  )
}

export default SearchBar
