import { useState } from 'react'

const SearchBar = ({ onSearch, loading }) => {
  const [query, setQuery] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    if (query.trim() && !loading) {
      onSearch(query.trim())
    }
  }

  return (
    <div className="card">
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="search" className="form-label">
            Search for Professionals
          </label>
          <input
            type="text"
            id="search"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="e.g., software engineers in San Francisco, marketing managers at tech companies"
            className="form-input"
            disabled={loading}
          />
        </div>

        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-500">
            Enter a description of the professionals you're looking for
          </p>
          <button
            type="submit"
            disabled={loading || !query.trim()}
            className="btn btn-primary"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </form>

      <div style={{marginTop: '1rem', fontSize: '0.75rem', color: '#9ca3af'}}>
        <p>Examples:</p>
        <ul style={{listStyle: 'disc', paddingLeft: '1.5rem', marginTop: '0.25rem'}}>
          <li>"Product managers at fintech startups"</li>
          <li>"Senior developers in New York"</li>
          <li>"Marketing directors at SaaS companies"</li>
        </ul>
      </div>
    </div>
  )
}

export default SearchBar
