#!/usr/bin/env python3
"""
Test script for Exa API integration
Run with: python test_exa_integration.py
"""

import os
import time
from dotenv import load_dotenv
from exa_py import Exa

# Load environment variables
load_dotenv('backend/.env')

def test_exa_api():
    """Test the Exa API integration"""
    
    # Get API key
    api_key = os.getenv('EXA_API_KEY')
    if not api_key or api_key == 'your_exa_api_key_here':
        print("❌ EXA_API_KEY not found or not configured in backend/.env")
        print("📝 Please add your actual Exa API key to backend/.env")
        return False
    
    print("🔍 Testing Exa API integration...")
    print(f"🔑 Using API key: {api_key[:10]}...")
    
    try:
        # Initialize Exa client
        exa = Exa(api_key)
        
        # Test search
        print("\n🔍 Testing professional search...")
        query = "software engineers at tech companies LinkedIn"
        
        search_response = exa.search(
            query=query,
            num_results=5,
            include_domains=["linkedin.com"],
            type="neural"
        )
        
        print(f"✅ Search successful! Found {len(search_response.results)} results")
        
        # Display results
        for i, result in enumerate(search_response.results[:3], 1):
            print(f"\n👤 Result {i}:")
            print(f"   Title: {result.title}")
            print(f"   URL: {result.url}")
            if hasattr(result, 'text') and result.text:
                print(f"   Preview: {result.text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Exa API test failed: {str(e)}")
        return False

def test_backend_api():
    """Test the backend API"""
    import requests
    
    print("\n🔍 Testing backend API...")
    
    try:
        # Test health check
        response = requests.get('http://localhost:3001/health', timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed")
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
        
        # Test search endpoint
        search_data = {"query": "software engineers in San Francisco"}
        response = requests.post('http://localhost:3001/api/search', json=search_data, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search API successful! Found {data.get('count', 0)} results")
            
            if data.get('results'):
                first_result = data['results'][0]
                print(f"👤 First result: {first_result.get('name')} - {first_result.get('title')}")
                return first_result.get('id')
        else:
            print(f"❌ Search API failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Is it running on port 3001?")
        return None
    except Exception as e:
        print(f"❌ Backend API test failed: {str(e)}")
        return None

def main():
    """Main test function"""
    print("🚀 TechWellness Agent Integration Test\n")
    
    # Test Exa API
    exa_success = test_exa_api()
    
    # Test Backend API
    profile_id = test_backend_api()
    
    # Summary
    print("\n📊 Test Summary:")
    print(f"   Exa API: {'✅ Working' if exa_success else '❌ Failed'}")
    print(f"   Backend API: {'✅ Working' if profile_id else '❌ Failed'}")
    
    if not exa_success:
        print("\n💡 To fix Exa API issues:")
        print("   1. Get an API key from https://exa.ai")
        print("   2. Add it to backend/.env as EXA_API_KEY=your_key_here")
    
    if not profile_id:
        print("\n💡 To fix Backend API issues:")
        print("   1. Make sure the backend is running: cd backend && node server.js")
        print("   2. Check that port 3001 is available")
    
    if exa_success and profile_id:
        print("\n🎉 All tests passed! The application is ready to use.")
        print("   Frontend: http://localhost:5173")
        print("   Backend: http://localhost:3001")

if __name__ == "__main__":
    main()
